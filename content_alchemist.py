import os
import google.generativeai as genai
from dotenv import load_dotenv

load_dotenv(dotenv_path="ai_content_amplifier/.env")

class ContentAlchemist:
    def __init__(self):
        self.api_key = os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            print("خطأ: مفتاح Gemini API غير موجود. يرجى تعيين GEMINI_API_KEY في ملف .env")
            return
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel("gemini-pro")

    def generate_thread(self, topic_title, topic_summary, topic_url):
        if not self.api_key:
            return ""

        prompt = f"""
        أنت 


خبير في صياغة سلاسل (Threads) جذابة ومثيرة للاهتمام على منصات التواصل الاجتماعي، خاصة X (تويتر سابقًا). مهمتك هي تحويل موضوع معين إلى سلسلة متكاملة ومترابطة، مع التركيز على الجودة العالية والصياغة الحصرية التي تجذب القراء وتدفعهم للتفاعل والمشاركة.

الموضوع الذي ستقوم بصياغة سلسلة عنه هو:
العنوان: {topic_title}
الملخص: {topic_summary}
الرابط الأصلي: {topic_url}

يجب أن تلتزم السلسلة بالهيكل والمتطلبات التالية:

1.  **الخطاف (Hook):** ابدأ السلسلة بجملة أو سؤال قوي ومثير للفضول يجذب الانتباه فورًا ويشجع على القراءة. يجب أن يكون الخطاف مرتبطًا بالموضوع ولكن بطريقة مبتكرة.

2.  **الصياغة الحصرية والجذابة:**
    *   يجب أن تكون اللغة المستخدمة حصرية ومبتكرة، بعيدة عن الكليشيهات. يمكنك استخدام أسلوب مثير للجدل (بناءً على حقائق) أو تعليمي عميق، حسب طبيعة الموضوع.
    *   ركز على تقديم قيمة حقيقية للقارئ، سواء كانت معلومة جديدة، منظور مختلف، أو تحليل عميق.
    *   استخدم لغة واضحة ومباشرة، وتجنب التعقيد غير الضروري.

3.  **الهيكلة المتسلسلة:**
    *   قسم السلسلة إلى تغريدات قصيرة ومترابطة (لا تتجاوز 280 حرفًا لكل تغريدة إن أمكن، مع مراعاة أن المنصات الأخرى قد تسمح بأكثر).
    *   يجب أن تكون كل تغريدة بمثابة خطوة منطقية تقود القارئ إلى التغريدة التالية.
    *   استخدم ترقيم التغريدات (مثال: 1/5، 2/5) لتوضيح التقدم.
    *   اجعل نهاية كل تغريدة مشجعة على الانتقال للتغريدة التالية (مثال: "لكن ماذا عن الجانب المظلم؟ تابع...").

4.  **دعوة للعمل (Call to Action - CTA):**
    *   في نهاية السلسلة، يجب أن تكون هناك دعوة واضحة ومحددة للقارئ للتفاعل. يمكن أن تكون:
        *   سؤالًا مفتوحًا لتشجيع التعليقات والنقاش.
        *   دعوة لإعادة التغريد (Retweet) أو المشاركة (Share).
        *   دعوة لزيارة الرابط الأصلي للموضوع لمزيد من التفاصيل.
        *   دعوة للمتابعة للحصول على المزيد من المحتوى المماثل.

5.  **الوسوم (Hashtags):**
    *   اختر 3-5 وسوم ذات صلة بالموضوع، وتكون شائعة ومستخدمة لزيادة الانتشار. ضعها في نهاية السلسلة.

**مثال على الهيكل العام:**

**التغريدة 1 (الخطاف):** [جملة قوية ومثيرة للفضول]...

**التغريدة 2:** [تطوير الفكرة الأولى]...

**التغريدة 3:** [تقديم تفاصيل أو زاوية جديدة]...

**التغريدة 4:** [تحليل أو مقارنة]...

**التغريدة 5 (الخاتمة + CTA + الوسوم):** [تلخيص أو استنتاج]... [دعوة للعمل] #هاشتاج1 #هاشتاج2 #هاشتاج3

الآن، بناءً على المعلومات التالية، قم بصياغة السلسلة:

العنوان: {topic_title}
الملخص: {topic_summary}
الرابط الأصلي: {topic_url}

تأكد من أن السلسلة كاملة وجاهزة للنشر مباشرة، ولا تضع أي تعليقات أو مقدمات إضافية. يجب أن تبدأ السلسلة مباشرة بالخطاف.


        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"خطأ أثناء توليد المحتوى باستخدام Gemini API: {e}")
            return ""

if __name__ == '__main__':
    alchemist = ContentAlchemist()
    # For testing, ensure GEMINI_API_KEY is set in your .env file
    # Example usage:
    # thread = alchemist.generate_thread(
    #     topic_title="الذكاء الاصطناعي يغير وجه الرعاية الصحية",
    #     topic_summary="كيف تساهم تقنيات الذكاء الاصطناعي في تحسين التشخيص والعلاج وتطوير الأدوية.",
    #     topic_url="https://example.com/ai-healthcare"
    # )
    # print(thread)



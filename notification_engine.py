import os
from dotenv import load_dotenv
import requests

load_dotenv(dotenv_path="ai_content_amplifier/.env")

class NotificationEngine:
    def __init__(self):
        self.telegram_bot_token = os.getenv("TELEGRAM_BOT_API_TOKEN")
        # You would typically get the chat_id from the user interacting with your bot first.
        # For this example, we'll assume a chat_id is provided or hardcoded for testing.
        # In a real application, you'd have a mechanism to store user chat_ids.
        self.telegram_chat_id = os.getenv("TELEGRAM_CHAT_ID") # Assuming this is also set in .env for simplicity

    def send_notification(self, message):
        if not self.telegram_bot_token:
            print("خطأ: مفتاح Telegram Bot API Token غير موجود. يرجى تعيين TELEGRAM_BOT_API_TOKEN في ملف .env")
            return False
        if not self.telegram_chat_id:
            print("خطأ: معرف دردشة Telegram غير موجود. يرجى تعيين TELEGRAM_CHAT_ID في ملف .env")
            return False

        url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
        payload = {
            "chat_id": self.telegram_chat_id,
            "text": message,
            "parse_mode": "HTML" # Optional: allows basic HTML formatting
        }

        try:
            response = requests.post(url, json=payload)
            response.raise_for_status()  # Raise an HTTPError for bad responses (4xx or 5xx)
            print("تم إرسال الإشعار بنجاح عبر Telegram.")
            return True
        except requests.exceptions.RequestException as e:
            print(f"خطأ أثناء إرسال الإشعار عبر Telegram: {e}")
            return False

if __name__ == '__main__':
    notifier = NotificationEngine()
    # For testing, ensure TELEGRAM_BOT_API_TOKEN and TELEGRAM_CHAT_ID are set in your .env file
    # You can get your chat_id by sending a message to your bot and then accessing:
    # https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
    # notifier.send_notification("هذا إشعار تجريبي من المهندس الخفي للانتشار!")



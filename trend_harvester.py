import os
import requests
from dotenv import load_dotenv

load_dotenv(dotenv_path='ai_content_amplifier/.env')

class TrendHarvester:
    def __init__(self):
        self.api_key = os.getenv('GOOGLE_NEWS_API_KEY')
        self.base_url = 'https://newsapi.org/v2/top-headlines'

    def get_trending_topics(self, query, country='sa', page_size=5):
        if not self.api_key:
            print("خطأ: مفتاح Google News API غير موجود. يرجى تعيين GOOGLE_NEWS_API_KEY في ملف .env")
            return []

        params = {
            'q': query,
            'country': country,
            'pageSize': page_size,
            'apiKey': self.api_key
        }

        try:
            response = requests.get(self.base_url, params=params)
            response.raise_for_status()  # Raise an HTTPError for bad responses (4xx or 5xx)
            data = response.json()
            articles = data.get('articles', [])

            trending_topics = []
            for article in articles:
                topic = {
                    'title': article.get('title'),
                    'summary': article.get('description'),
                    'url': article.get('url')
                }
                trending_topics.append(topic)
            return trending_topics
        except requests.exceptions.RequestException as e:
            print(f"خطأ أثناء الاتصال بـ Google News API: {e}")
            return []

if __name__ == '__main__':
    harvester = TrendHarvester()
    # For testing, ensure GOOGLE_NEWS_API_KEY is set in your .env file
    # Example usage:
    # topics = harvester.get_trending_topics(query='الذكاء الاصطناعي', country='sa')
    # for i, topic in enumerate(topics):
    #     print(f"\nالموضوع {i+1}:")
    #     print(f"العنوان: {topic['title']}")
    #     print(f"الملخص: {topic['summary']}")
    #     print(f"الرابط: {topic['url']}")



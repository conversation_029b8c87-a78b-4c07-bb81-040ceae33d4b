# توثيق نظام المهندس الخفي للانتشار

هذا المستند يوفر إرشادات حول كيفية إعداد وتشغيل نظام "المهندس الخفي للانتشار"، وهو وكيل آلي مصمم لتوليد وتضخيم المحتوى على منصات التواصل الاجتماعي (X ولينكدإن) بشكل حصري ومستهدف حول مواضيع الذكاء الاصطناعي.

## 1. نظرة عامة على النظام

يتكون النظام من عدة أدوات تعمل بشكل متكامل:

*   **Trend Harvester:** للبحث عن أهم المواضيع الرائجة في مجال الذكاء الاصطناعي باستخدام Google News API.
*   **Content Alchemist:** لتوليد سلاسل (Threads) حصرية وجذابة باستخدام Gemini API.
*   **Amplification Engine:** لنشر السلاسل تلقائيًا على منصات X (تويتر سابقًا) ولينكدإن.
*   **Notification Engine:** لإرسال إشعارات عبر Telegram عند اكتمال العمليات.

يعمل النظام بشكل دوري (كل 6 ساعات) للبحث عن مواضيع جديدة، توليد المحتوى، ونشره، ثم إرسال إشعار بالنتائج.

## 2. المتطلبات الأساسية

لضمان عمل النظام بشكل صحيح، تحتاج إلى توفير المتطلبات التالية:

*   **Python 3.x:** يجب أن يكون Python مثبتًا على نظامك.
*   **مفاتيح API:** ستحتاج إلى مفاتيح API من الخدمات التالية:
    *   Google News API
    *   Gemini API
    *   X (Twitter) API (Consumer Key, Consumer Secret, Access Token, Access Token Secret)
    *   LinkedIn API (Client ID, Client Secret, Access Token)
    *   Telegram Bot API Token
    *   Telegram Chat ID (معرف الدردشة الخاص بك لتلقي الإشعارات)

## 3. إعداد البيئة

اتبع الخطوات التالية لإعداد بيئة المشروع:

### أ. استنساخ المشروع (إذا لم يكن موجودًا)

إذا لم يكن لديك مجلد المشروع `ai_content_amplifier`، يمكنك افتراضيًا أنشأته لك. يمكنك الانتقال إليه:

```bash
cd ai_content_amplifier
```

### ب. تثبيت التبعيات

تأكد من تثبيت المكتبات المطلوبة باستخدام `pip`:

```bash
pip install -r config/requirements.txt
```

### ج. إعداد ملف `.env`

قم بإنشاء ملف باسم `.env` في المجلد الرئيسي للمشروع (`ai_content_amplifier/`) واملأه بمفاتيح الـ API الخاصة بك. يجب أن يبدو الملف كالتالي (استبدل `YOUR_API_KEY` بالقيم الفعلية):

```
GOOGLE_NEWS_API_KEY=YOUR_GOOGLE_NEWS_API_KEY
GEMINI_API_KEY=YOUR_GEMINI_API_KEY
X_API_KEY=YOUR_X_API_KEY
# X_API_SECRET=YOUR_X_API_SECRET # إذا كان مطلوبًا من مكتبة X API
# X_ACCESS_TOKEN=YOUR_X_ACCESS_TOKEN
# X_ACCESS_TOKEN_SECRET=YOUR_X_ACCESS_TOKEN_SECRET
LINKEDIN_API_KEY=YOUR_LINKEDIN_API_KEY
# LINKEDIN_CLIENT_SECRET=YOUR_LINKEDIN_CLIENT_SECRET # إذا كان مطلوبًا من مكتبة LinkedIn API
# LINKEDIN_ACCESS_TOKEN=YOUR_LINKEDIN_ACCESS_TOKEN
TELEGRAM_BOT_API_TOKEN=YOUR_TELEGRAM_BOT_API_TOKEN
TELEGRAM_CHAT_ID=YOUR_TELEGRAM_CHAT_ID
```

**ملاحظات هامة:**

*   بالنسبة لـ X (تويتر) ولينكدإن، قد تحتاج إلى مفاتيح إضافية (مثل `Consumer Secret`, `Access Token`, `Access Token Secret` لـ X، و `Client Secret`, `Access Token` لـ LinkedIn) اعتمادًا على مكتبة الـ API التي ستستخدمها وطريقة المصادقة. الكود الحالي يستخدم مفتاح API واحدًا للتبسيط، ولكن في تطبيق حقيقي ستحتاج إلى إعداد مصادقة OAuth كاملة.
*   للحصول على `TELEGRAM_CHAT_ID`، يمكنك بدء محادثة مع البوت الخاص بك على Telegram، ثم زيارة الرابط التالي في متصفحك (استبدل `YOUR_TELEGRAM_BOT_API_TOKEN` بالرمز الخاص بك): `https://api.telegram.org/botYOUR_TELEGRAM_BOT_API_TOKEN/getUpdates`. ابحث عن قيمة `id` ضمن كائن `chat`.

## 4. تشغيل النظام

بعد إعداد ملف `.env` وتثبيت التبعيات، يمكنك تشغيل النظام باستخدام الأمر التالي من المجلد الرئيسي للمشروع (`ai_content_amplifier/`):

```bash
python src/main_agent.py
```

سيبدأ النظام في العمل بشكل دوري، وسيقوم بالخطوات التالية:

1.  البحث عن المواضيع الرائجة في الذكاء الاصطناعي كل 6 ساعات.
2.  توليد سلسلة محتوى فريدة لكل موضوع.
3.  نشر السلسلة على X ولينكدإن.
4.  إرسال إشعار عبر Telegram بعد كل عملية نشر.

## 5. إيقاف النظام

لإيقاف تشغيل النظام، يمكنك ببساطة الضغط على `Ctrl+C` في نافذة الطرفية التي يتم تشغيل النظام فيها.

## 6. استكشاف الأخطاء وإصلاحها

*   **"خطأ: مفتاح API غير موجود"**: تأكد من أن جميع مفاتيح الـ API المطلوبة موجودة في ملف `.env` وأن أسماء المتغيرات صحيحة.
*   **"خطأ أثناء الاتصال بـ Google News API"**: تحقق من اتصالك بالإنترنت ومن صحة مفتاح Google News API الخاص بك.
*   **"خطأ أثناء توليد المحتوى باستخدام Gemini API"**: تأكد من صحة مفتاح Gemini API الخاص بك وأنك لم تتجاوز حدود الاستخدام.
*   **"فشل نشر السلسلة"**: تحقق من صحة مفاتيح API الخاصة بـ X ولينكدإن، وتأكد من أن حساباتك مفعلة وتسمح بالنشر عبر API.
*   **"خطأ أثناء إرسال الإشعار عبر Telegram"**: تأكد من صحة `TELEGRAM_BOT_API_TOKEN` و `TELEGRAM_CHAT_ID`.

---


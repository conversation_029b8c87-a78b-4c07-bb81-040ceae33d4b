import time
import os
from dotenv import load_dotenv

from tools.trend_harvester import TrendHarvester
from tools.content_alchemist import ContentAlchemist
from tools.amplification_engine import AmplificationEngine
from tools.notification_engine import NotificationEngine

load_dotenv(dotenv_path="ai_content_amplifier/.env")

def run_agent():
    print("بدء تشغيل المهندس الخفي للانتشار...")

    harvester = TrendHarvester()
    alchemist = ContentAlchemist()
    amplifier = AmplificationEngine()
    notifier = NotificationEngine()

    # Check if all necessary API keys are available
    if not harvester.api_key:
        print("توقف: مفتاح Google News API مفقود.")
        return
    if not alchemist.api_key:
        print("توقف: مفتاح Gemini API مفقود.")
        return
    if not amplifier.x_api_key or not amplifier.linkedin_api_key:
        print("توقف: مفاتيح X أو LinkedIn API مفقودة.")
        return
    if not notifier.telegram_bot_token or not notifier.telegram_chat_id:
        print("توقف: مفتاح Telegram Bot API Token أو Telegram Chat ID مفقود.")
        return

    while True:
        print("\n-- بدء دورة جديدة --")
        try:
            # 1. البحث عن التوجهات
            print("البحث عن المواضيع الرائجة في الذكاء الاصطناعي...")
            topics = harvester.get_trending_topics(query=\'الذكاء الاصطناعي\', country=\'sa\')

            if not topics:
                print("لم يتم العثور على مواضيع رائجة. المحاولة مرة أخرى في 6 ساعات.")
                notifier.send_notification("لم يتم العثور على مواضيع رائجة في الذكاء الاصطناعي. المحاولة مرة أخرى في 6 ساعات.")
            else:
                print(f"تم العثور على {len(topics)} مواضيع رائجة.")
                for i, topic in enumerate(topics):
                    print(f"\nمعالجة الموضوع {i+1}: {topic['title']}")

                    # 2. توليد سلسلة فريدة
                    print("توليد السلسلة...")
                    thread_content = alchemist.generate_thread(
                        topic_title=topic['title'],
                        topic_summary=topic['summary'],
                        topic_url=topic['url']
                    )

                    if thread_content:
                        print("تم توليد السلسلة بنجاح. النشر على المنصات...")
                        # 3. نشر السلسلة على جميع المنصات في أوقات الذروة (محاكاة)
                        publish_success = amplifier.publish_thread(thread_content)

                        # 4. إرسال إشعار بعد كل نشر
                        if publish_success:
                            notification_msg = f"تم نشر سلسلة جديدة بنجاح حول: {topic['title']}"
                            print(notification_msg)
                            notifier.send_notification(notification_msg)
                        else:
                            notification_msg = f"فشل نشر السلسلة حول: {topic['title']}"
                            print(notification_msg)
                            notifier.send_notification(notification_msg)
                    else:
                        print(f"فشل توليد السلسلة للموضوع: {topic['title']}")
                        notifier.send_notification(f"فشل توليد السلسلة للموضوع: {topic['title']}")

        except Exception as e:
            print(f"حدث خطأ عام في الدورة: {e}")
            notifier.send_notification(f"خطأ في المهندس الخفي للانتشار: {e}")

        print("\n-- انتهاء الدورة. الانتظار لمدة 6 ساعات قبل الدورة التالية --")
        time.sleep(6 * 3600) # الانتظار 6 ساعات (6 * 3600 ثانية)

if __name__ == '__main__':
    run_agent()


